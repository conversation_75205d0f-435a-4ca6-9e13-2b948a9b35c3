import { PerformanceMeasurementDecorator, type Logger } from '@discocil/fv-domain-library';
import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { EventDependencyIdentifier } from '@/events/events/domain/dependencyIdentifier/EventDependencyIdentifier';
import { DeletePaymentUseCase } from '@/payments/application/DeletePaymentUseCase';
import { SearchPaymentsUseCase } from '@/payments/application/SearchPaymentsUseCase';
import { PaymentDependencyIdentifier } from '@/payments/domain/dependencyIdentifier/PaymentDependencyIdentifier';
import { TicketDependencyIdentifier } from '@/tickets/tickets/domain/dependencyIdentifier/TicketDependencyIdentifier';

import { PaymentCacheRepository } from '../database/repositories/PaymentCacheRepository';
import { PaymentMongoRepository } from '../database/repositories/PaymentMongoRepository';

import type { ICacheRepository } from '@/cross-cutting/domain/contracts/CacheHandler';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { CacheConfig } from '@/cross-cutting/infrastructure/services/CacheConfig';
import type { DependencyContainer } from 'tsyringe';

export const PaymentContainer = {
  register: (): void => {
    container.register(PaymentDependencyIdentifier.PaymentRepository, {
      useFactory: instanceCachingFactory((container) => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);
        const mongoRepository = new PaymentMongoRepository(dbConnection);

        const cacheHander = container.resolve<ICacheRepository>(DependencyIdentifier.ICacheHandler);
        const cacheConfig = container.resolve<CacheConfig>(DependencyIdentifier.CacheConfig);

        return new PaymentCacheRepository(cacheHander, cacheConfig, mongoRepository);
      }),
    });

    container.register(SearchPaymentsUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new SearchPaymentsUseCase(
          container.resolve(EventDependencyIdentifier.EventRepository),
          container.resolve(PaymentDependencyIdentifier.PaymentRepository),
          container.resolve(TicketDependencyIdentifier.TicketRepository),
        );

        return new PerformanceMeasurementDecorator(
          SearchPaymentsUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(DeletePaymentUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new DeletePaymentUseCase(
          container.resolve(PaymentDependencyIdentifier.PaymentRepository),
        );

        return new PerformanceMeasurementDecorator(
          DeletePaymentUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });
  },
};
